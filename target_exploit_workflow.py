#!/usr/bin/env python3
"""
Target Exploit Workflow - Python Implementation
Converted from Dify DSL workflow for OSINT research
"""

import json
import asyncio
import aiohttp
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import os
import subprocess
import re
import logging
from abc import ABC, abstractmethod


@dataclass
class WorkflowInput:
    """Input variables for the workflow"""
    location: str
    interest: str


@dataclass
class SubdomainInfo:
    """Information about a subdomain including its IP address"""
    domain: str
    ip_address: Optional[str] = None
    dns_error: Optional[str] = None


@dataclass
class EntityResult:
    """Result structure for entity extraction"""
    entity: Optional[str]
    link: Optional[str]
    domain: Optional[str]
    subdomains: Optional[List[str]] = None
    subdomain_ips: Optional[List[SubdomainInfo]] = None
    location_verification: Optional[Dict[str, Any]] = None
    subdomain_location_verification: Optional[List[Dict[str, Any]]] = None


async def resolve_domain_ip_google_dns(domain: str, timeout: int = 5) -> SubdomainInfo:
    """
    Resolve IP address for a domain using Google DNS API

    Args:
        domain: Domain name to resolve
        timeout: Timeout in seconds for the HTTP request

    Returns:
        SubdomainInfo object with domain and IP address or error
    """
    logger = logging.getLogger(__name__)

    try:
        # Use Google DNS API to resolve A record
        url = f"https://dns.google/resolve?name={domain}&type=A"

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    data = await response.json()

                    # Check if we got a valid response
                    if data.get('Status') == 0:  # NOERROR
                        answers = data.get('Answer', [])

                        # Filter A records (type 1) and extract IP addresses
                        ip_addresses = []
                        for answer in answers:
                            if answer.get('type') == 1:  # A record
                                ip_data = answer.get('data')
                                if ip_data and re.match(r'^\d+\.\d+\.\d+\.\d+$', ip_data):
                                    ip_addresses.append(ip_data)

                        if ip_addresses:
                            logger.debug(f"Resolved {domain} to {ip_addresses[0]} via Google DNS")
                            return SubdomainInfo(domain=domain, ip_address=ip_addresses[0])
                        else:
                            logger.warning(f"No A record found for {domain}")
                            return SubdomainInfo(domain=domain, dns_error="No A record found")
                    else:
                        # DNS error codes: 1=FORMERR, 2=SERVFAIL, 3=NXDOMAIN, etc.
                        status = data.get('Status', 'Unknown')
                        logger.warning(f"DNS query failed for {domain}: Status {status}")
                        return SubdomainInfo(domain=domain, dns_error=f"DNS query failed: Status {status}")
                else:
                    logger.warning(f"HTTP error {response.status} for {domain}")
                    return SubdomainInfo(domain=domain, dns_error=f"HTTP error: {response.status}")

    except asyncio.TimeoutError:
        logger.warning(f"DNS resolution timeout for {domain}")
        return SubdomainInfo(domain=domain, dns_error="DNS resolution timeout")
    except aiohttp.ClientError as e:
        logger.warning(f"HTTP client error for {domain}: {e}")
        return SubdomainInfo(domain=domain, dns_error=f"HTTP client error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error resolving {domain}: {e}")
        return SubdomainInfo(domain=domain, dns_error=f"Unexpected error: {str(e)}")


def resolve_domain_ip(domain: str, timeout: int = 5) -> SubdomainInfo:
    """
    Resolve IP address for a domain using dig command (fallback method)

    Args:
        domain: Domain name to resolve
        timeout: Timeout in seconds for the dig command

    Returns:
        SubdomainInfo object with domain and IP address or error
    """
    logger = logging.getLogger(__name__)

    try:
        # Use dig command to resolve A record
        cmd = ["dig", "+short", "+time={}".format(timeout), domain, "A"]
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout + 2  # Add buffer to subprocess timeout
        )

        if result.returncode == 0:
            output = result.stdout.strip()
            if output:
                # Parse the output - dig +short returns IP addresses, one per line
                lines = output.split('\n')
                # Filter out CNAME records and get the first IP address
                ip_addresses = [line.strip() for line in lines if re.match(r'^\d+\.\d+\.\d+\.\d+$', line.strip())]

                if ip_addresses:
                    logger.debug(f"Resolved {domain} to {ip_addresses[0]}")
                    return SubdomainInfo(domain=domain, ip_address=ip_addresses[0])
                else:
                    logger.warning(f"No A record found for {domain}")
                    return SubdomainInfo(domain=domain, dns_error="No A record found")
            else:
                logger.warning(f"Empty response for {domain}")
                return SubdomainInfo(domain=domain, dns_error="Empty DNS response")
        else:
            error_msg = result.stderr.strip() if result.stderr else "Unknown dig error"
            logger.warning(f"dig command failed for {domain}: {error_msg}")
            return SubdomainInfo(domain=domain, dns_error=f"dig failed: {error_msg}")

    except subprocess.TimeoutExpired:
        logger.warning(f"DNS resolution timeout for {domain}")
        return SubdomainInfo(domain=domain, dns_error="DNS resolution timeout")
    except FileNotFoundError:
        logger.error("dig command not found. Please install dnsutils package.")
        return SubdomainInfo(domain=domain, dns_error="dig command not available")
    except Exception as e:
        logger.error(f"Unexpected error resolving {domain}: {e}")
        return SubdomainInfo(domain=domain, dns_error=f"Unexpected error: {str(e)}")


async def resolve_subdomains_ips(subdomains: List[str], timeout: int = 5, use_google_dns: bool = True) -> List[SubdomainInfo]:
    """
    Resolve IP addresses for multiple subdomains concurrently using Google DNS API

    Args:
        subdomains: List of domain names to resolve
        timeout: Timeout in seconds for each DNS query
        use_google_dns: Whether to use Google DNS API (True) or fallback to dig command (False)

    Returns:
        List of SubdomainInfo objects with IP resolution results
    """
    logger = logging.getLogger(__name__)

    if not subdomains:
        return []

    logger.info(f"Resolving IP addresses for {len(subdomains)} subdomains using {'Google DNS API' if use_google_dns else 'dig command'}...")

    tasks = []

    if use_google_dns:
        # Use Google DNS API for concurrent resolution
        for domain in subdomains:
            task = resolve_domain_ip_google_dns(domain, timeout)
            tasks.append(task)

        # Wait for all DNS resolutions to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
    else:
        # Fallback to dig command using thread pool
        loop = asyncio.get_event_loop()
        for domain in subdomains:
            # Run the synchronous dig command in a thread pool
            task = loop.run_in_executor(None, resolve_domain_ip, domain, timeout)
            tasks.append(task)

        # Wait for all DNS resolutions to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and handle any exceptions
    subdomain_infos = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Exception resolving {subdomains[i]}: {result}")
            subdomain_infos.append(SubdomainInfo(
                domain=subdomains[i],
                dns_error=f"Exception: {str(result)}"
            ))
        else:
            subdomain_infos.append(result)

    # Log summary
    successful_resolutions = len([info for info in subdomain_infos if info.ip_address])
    logger.info(f"Successfully resolved {successful_resolutions}/{len(subdomains)} subdomains")

    return subdomain_infos


class BaseNode(ABC):
    """Base class for workflow nodes"""
    
    def __init__(self, node_id: str, title: str):
        self.node_id = node_id
        self.title = title
    
    @abstractmethod
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the node logic"""
        pass


class WikipediaSearchNode(BaseNode):
    """Wikipedia search tool node"""
    
    def __init__(self):
        super().__init__("1747620225282", "维基百科搜索")
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Search Wikipedia for the given query"""
        query = f"{inputs['interest']}{inputs['location']}"
        language = "en"  # English
        
        # Simulate Wikipedia API call
        async with aiohttp.ClientSession() as session:
            url = f"https://en.wikipedia.org/api/rest_v1/page/summary/{query}"
            try:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "text": data.get("extract", ""),
                            "title": data.get("title", ""),
                            "url": data.get("content_urls", {}).get("desktop", {}).get("page", "")
                        }
            except Exception as e:
                print(f"Wikipedia search error: {e}")
        
        return {"text": "", "title": "", "url": ""}


class SerpSearchNode(BaseNode):
    """SERP search tool node"""

    def __init__(self, node_id: str, title: str):
        super().__init__(node_id, title)
        self.google_country = os.getenv("google_country", "jp")
        self.google_lan = os.getenv("google_lan", "en")

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform SERP search"""
        # This would require actual SERP API integration
        # For now, return mock data structure
        return {
            "json": [
                {
                    "title": f"Sample result for {inputs.get('query', '')}",
                    "snippet": "Sample snippet content",
                    "link": "https://example.com",
                    "displayLink": "example.com"
                }
            ]
        }


class SerperSearchNode(BaseNode):
    """Serper search tool node (serper.dev)"""

    def __init__(self, node_id: str, title: str):
        super().__init__(node_id, title)
        self.google_country = os.getenv("google_country", "jp")
        self.google_lan = os.getenv("google_lan", "en")

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Perform Serper search"""
        # This would require actual Serper API integration
        # For now, return mock data structure
        return {
            "json": [
                {
                    "title": f"Sample Serper result for {inputs.get('query', '')}",
                    "snippet": "Sample Serper snippet content",
                    "link": "https://example.com",
                    "displayLink": "example.com"
                }
            ]
        }


class LLMNode(BaseNode):
    """Large Language Model node for entity extraction and analysis"""
    
    def __init__(self, node_id: str, title: str, prompt_template: str, model_name: str = "qwen3:32b"):
        super().__init__(node_id, title)
        self.prompt_template = prompt_template
        self.model_name = model_name
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute LLM inference"""
        # This would require actual LLM API integration (Ollama, OpenAI, etc.)
        # For now, return mock structured output
        
        if "实体抽取" in self.title:
            return {
                "text": json.dumps({
                    "entities": ["Entity1", "Entity2", "Entity3"]
                })
            }
        elif "实体域名抽取" in self.title:
            return {
                "text": json.dumps({
                    "entity": inputs.get("item", ""),
                    "link": "https://example.com"
                })
            }
        elif "LLM知识" in self.title:
            return {
                "text": json.dumps({
                    "entities": ["KnowledgeEntity1", "KnowledgeEntity2"]
                })
            }
        
        return {"text": "{}"}


class TemplateTransformNode(BaseNode):
    """Template transformation node"""
    
    def __init__(self, node_id: str, title: str, template: str):
        super().__init__(node_id, title)
        self.template = template
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Transform input using template"""
        # Simple template processing
        if "{% for entry in arg1 %}" in self.template:
            # Handle array iteration template
            arg1 = inputs.get("arg1", [])
            output = "\n".join(str(entry) for entry in arg1)
        else:
            # Handle simple variable substitution
            output = inputs.get("text", "") or inputs.get("arg1", "")
        
        return {"output": output}


class CodeExecutionNode(BaseNode):
    """Code execution node"""
    
    def __init__(self, node_id: str, title: str, code: str):
        super().__init__(node_id, title)
        self.code = code
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Python code"""
        if "json.loads" in self.code and "entities" in self.code:
            # Entity extraction code
            text = inputs.get("text", "{}")
            try:
                res = json.loads(text)
                return {"entities": res.get('entities', [])}
            except json.JSONDecodeError:
                return {"entities": []}
        
        elif "urlparse" in self.code:
            # Domain extraction code
            text = inputs.get("text", "{}")
            try:
                res = json.loads(text)
                link = res.get('link', '')
                if link:
                    domain = urlparse(link).netloc
                    domain = domain.split('www.')[-1]
                else:
                    domain = ''
                return {'result': domain}
            except (json.JSONDecodeError, Exception):
                return {'result': ''}
        
        return {}


class IterationNode(BaseNode):
    """Iteration node for processing arrays"""
    
    def __init__(self):
        super().__init__("1747626477378", "迭代")
        self.child_nodes = []
    
    def add_child_node(self, node: BaseNode):
        """Add a child node to the iteration"""
        self.child_nodes.append(node)
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute iteration over entities"""
        entities = inputs.get("entities", [])
        results = []
        
        for entity in entities:
            # Execute child nodes for each entity
            item_inputs = {**inputs, "item": entity}
            
            for node in self.child_nodes:
                item_inputs.update(await node.execute(item_inputs))
            
            # Extract final result
            if "result" in item_inputs:
                results.append(item_inputs["result"])
        
        return {"output": results}


class TargetExploitWorkflow:
    """Main workflow orchestrator"""
    
    def __init__(self):
        self.nodes = {}
        self._setup_nodes()
    
    def _setup_nodes(self):
        """Initialize all workflow nodes"""
        # Wikipedia search
        self.nodes["1747620225282"] = WikipediaSearchNode()
        
        # SERP searches
        self.nodes["1747658166995"] = SerpSearchNode("1747658166995", "SERP搜索")
        self.nodes["1747658853097"] = SerpSearchNode("1747658853097", "SERP搜索2")
        
        # LLM nodes
        entity_extraction_prompt = """You are an OSINT assistant and now investing a targeted area. please list {interest} in {location} according to search result. You can identify and filter entities by snippet and title fields.

## Output
- Do not output entities that are the same as already output.
- Do not output historic entity.
- Please output in json format.

```json
entities: array[string] | None
```"""
        
        self.nodes["17476158901620"] = LLMNode("17476158901620", "实体抽取", entity_extraction_prompt)
        self.nodes["1747812601228"] = LLMNode("1747812601228", "LLM知识", "Give some important entities")
        self.nodes["1747638146739"] = LLMNode("1747638146739", "实体域名抽取", "Find domain name")
        
        # Template transforms
        self.nodes["1747625155858"] = TemplateTransformNode("1747625155858", "大模型输出转换", "{{ text }}")
        self.nodes["1747658446946"] = TemplateTransformNode("1747658446946", "模板转换 5", "{% for entry in arg1 %}\r\n{{ entry }}\r\n{% endfor %}")
        self.nodes["1747814043414"] = TemplateTransformNode("1747814043414", "大模型输出转换", "{{ arg1 }}")
        self.nodes["1747658871649"] = TemplateTransformNode("1747658871649", "模板转换 6", "{% for entry in arg1 %}\r\n{{ entry }}\r\n{% endfor %}")
        self.nodes["1747639494483"] = TemplateTransformNode("1747639494483", "模板转换 4", "{{ arg1 }}")
        
        # Code execution
        entity_code = """
def main(text) -> dict:
    res = json.loads(text)
    return {
        "entities": res.get('entities', []),
    }
"""
        self.nodes["1747625285578"] = CodeExecutionNode("1747625285578", "代码执行", entity_code)
        
        domain_code = """
from urllib.parse import urlparse

def main(text) -> dict:
    res = json.loads(text)
    link = res.get('link', '')
    if link:
        domain = urlparse(link).netloc
    else:
        domain = ''
    domain = domain.split('www.')[-1]
    return {'result': domain}
"""
        self.nodes["1749176245192"] = CodeExecutionNode("1749176245192", "提取域名", domain_code)
        
        # Iteration
        iteration_node = IterationNode()
        iteration_node.add_child_node(self.nodes["1747658853097"])  # Google search 2
        iteration_node.add_child_node(self.nodes["1747658871649"])  # Template transform 6
        iteration_node.add_child_node(self.nodes["1747638146739"])  # Entity domain extraction
        iteration_node.add_child_node(self.nodes["1747639494483"])  # Template transform 4
        iteration_node.add_child_node(self.nodes["1749176245192"])  # Domain extraction
        
        self.nodes["1747626477378"] = iteration_node
    
    async def execute(self, workflow_input: WorkflowInput) -> List[str]:
        """Execute the complete workflow"""
        print(f"Starting workflow for location: {workflow_input.location}, interest: {workflow_input.interest}")
        
        # Convert input to dict
        inputs = {
            "location": workflow_input.location,
            "interest": workflow_input.interest
        }
        
        # Step 1: Wikipedia search
        wiki_result = await self.nodes["1747620225282"].execute(inputs)
        inputs.update(wiki_result)
        
        # Step 2: Google search
        google_result = await self.nodes["1747658166995"].execute({**inputs, "query": f"{inputs['interest']} {inputs['location']}"})
        inputs.update(google_result)
        
        # Step 3: Template transform for Google results
        template_result = await self.nodes["1747658446946"].execute({"arg1": google_result.get("json", [])})
        inputs.update(template_result)
        
        # Step 4: LLM knowledge extraction
        llm_knowledge = await self.nodes["1747812601228"].execute(inputs)
        inputs.update(llm_knowledge)
        
        # Step 5: Template transform for LLM output
        llm_template = await self.nodes["1747814043414"].execute({"arg1": llm_knowledge.get("text", "")})
        inputs.update(llm_template)
        
        # Step 6: Entity extraction LLM
        entity_extraction = await self.nodes["17476158901620"].execute(inputs)
        inputs.update(entity_extraction)
        
        # Step 7: Transform LLM output
        entity_transform = await self.nodes["1747625155858"].execute({"text": entity_extraction.get("text", "")})
        inputs.update(entity_transform)
        
        # Step 8: Code execution to parse entities
        code_result = await self.nodes["1747625285578"].execute({"text": entity_transform.get("output", "")})
        inputs.update(code_result)
        
        # Step 9: Iteration over entities
        iteration_result = await self.nodes["1747626477378"].execute(inputs)
        
        return iteration_result.get("output", [])


async def main():
    """Main execution function"""
    # Example usage
    workflow = TargetExploitWorkflow()
    
    # Create input
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    # Execute workflow
    results = await workflow.execute(workflow_input)
    
    print("Workflow Results:")
    for i, result in enumerate(results, 1):
        print(f"{i}. {result}")


if __name__ == "__main__":
    asyncio.run(main())
